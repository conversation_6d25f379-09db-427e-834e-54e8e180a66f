{% set title = "通知设置" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block head%}
<!-- PWA支持 -->
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<!-- 主题样式由 theme-manager.js 动态管理 -->

<!-- 页面特定样式 -->
<style>
    /* 确保SVG图标不受强调色影响 */
    .ti {
        color: inherit !important;
    }

    /* 正确设置强调色图标 */
    .text-blue-400 .ti,
    .text-blue-500 .ti {
        color: inherit !important;
    }

    /* 侧边栏菜单项图标颜色 */
    #admin-sidebar .ti {
        color: var(--color-slate-500) !important;
    }

    #admin-sidebar a:hover .ti,
    #admin-sidebar a.active .ti,
    #admin-sidebar .text-blue-400 .ti {
        color: var(--color-purple-400) !important;
    }

    /* 右上角徽标已移除（保留占位注释以便追踪历史） */
</style>

<!-- 背景加载脚本 -->
<script>
// 初始化背景设置
document.addEventListener('DOMContentLoaded', function() {
    const body = document.body;

    // 从localStorage获取背景设置
    try {
        // 尝试从localStorage中获取本地保存的背景设置
        const personalizationSettings = localStorage.getItem('personalization_settings');
        if (personalizationSettings) {
            const settings = JSON.parse(personalizationSettings);
            const background = settings.background;
            
            if (background && background.enabled) {
                if (background.type === 'image' && background.imageUrl) {
                    body.style.setProperty('--bg-image', `url(${background.imageUrl})`);
                    body.classList.add('bg-custom-image');
                } else if (background.type === 'gradient' && background.gradient) {
                    body.style.setProperty('--bg-gradient', background.gradient);
                    body.classList.add('bg-custom-gradient');
                }
                
                if (background.blur) {
                    body.style.setProperty('--bg-blur', `${background.blur}px`);
                    body.classList.add('bg-blur');
                }
            }
        }
    } catch (error) {
        console.error('加载背景设置失败:', error);
    }
});
</script>
{%endblock%}

{%block content%}
<!-- 页面容器 - 简化布局 -->
<div>
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 - 独立卡片布局 -->
    <div class="flex-1 space-y-6">

        <!-- 页面标题卡片 - 独立设计 -->
        <div class="admin-card">
            <div class="admin-card-header">
                <div class="admin-page-header">
                    <div class="admin-page-title">
                        <div class="admin-title-icon admin-icon-purple">
                            <i class="ti ti-bell text-lg text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="admin-title-text">
                            <h1>通知设置</h1>
                            <p class="text-xs">Notification Settings</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通知类型统一管理区 - 独立置顶容器 -->
        <div class="admin-card mb-6">
            <div class="admin-list-header">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900/40 dark:to-indigo-800/30 rounded-lg shadow-sm flex-shrink-0 border border-indigo-200/50 dark:border-indigo-700/30 flex items-center justify-center">
                            <i class="ti ti-settings text-base text-indigo-600 dark:text-indigo-400"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h2 class="text-base font-medium text-slate-800 dark:text-slate-200">通知类型管理</h2>
                            <p class="text-xs text-slate-500 dark:text-slate-400">统一管理所有通道的通知类型开关</p>
                        </div>
                    </div>
                    <!-- 右侧提示徽标按需移除 -->
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 通知类型矩阵 - 响应式表格 -->
                <div class="overflow-x-auto">
                    <table class="w-full min-w-[600px]">
                        <thead>
                            <tr class="border-b border-slate-200 dark:border-slate-700">
                                <th class="text-left py-3 px-4 text-sm font-medium text-slate-700 dark:text-slate-300 min-w-[120px]">
                                    <div class="flex items-center gap-2">
                                        <i class="ti ti-bell text-sm"></i>
                                        通知类型
                                    </div>
                                </th>
                                <th class="text-center py-3 px-4 text-sm font-medium text-slate-700 dark:text-slate-300 min-w-[100px]">
                                    <div class="flex items-center justify-center gap-2">
                                        <i class="ti ti-brand-telegram text-sm text-cyan-500"></i>
                                        <span class="hidden sm:inline">Telegram</span>
                                        <span class="sm:hidden">TG</span>
                                    </div>
                                </th>
                                <th class="text-center py-3 px-4 text-sm font-medium text-slate-700 dark:text-slate-300 min-w-[100px]">
                                    <div class="flex items-center justify-center gap-2">
                                        <i class="ti ti-mail text-sm text-rose-500"></i>
                                        <span class="hidden sm:inline">邮件</span>
                                        <span class="sm:hidden">Mail</span>
                                    </div>
                                </th>
                                <th class="text-center py-3 px-4 text-sm font-medium text-slate-700 dark:text-slate-300 min-w-[80px]">
                                    <div class="flex items-center justify-center gap-2">
                                        <i class="ti ti-toggle-left text-sm"></i>
                                        <span class="hidden sm:inline">切换</span>
                                        <span class="sm:hidden">切换</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="unified-notification-matrix" class="divide-y divide-slate-200 dark:divide-slate-700">
                            <!-- 动态生成通知类型矩阵 -->
                        </tbody>
                    </table>
                </div>

                <!-- 操作按钮区域 -->
                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <div class="flex flex-wrap items-center gap-3">
                        <button onclick="saveUnifiedNotificationTypes()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200">
                            <i class="ti ti-device-floppy text-sm"></i>
                            保存所有设置
                        </button>
                        <button onclick="resetNotificationTypes()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-xl border border-slate-200 dark:border-slate-700 hover:bg-slate-200 dark:hover:bg-slate-700 transition-all duration-200">
                            <i class="ti ti-refresh text-sm"></i>
                            恢复默认
                        </button>
                    </div>
                    <div class="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
                        <i class="ti ti-info-circle"></i>
                        <span>仅显示已配置且启用的通道</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Telegram通知设置卡片 - 折叠 -->
        <div class="admin-card">
            <!-- 头部：可折叠，含状态与操作 -->
            <div class="admin-list-header flex items-center justify-between gap-3 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors duration-200 rounded-lg -mx-2 px-2" onclick="toggleCard('tg')" title="点击展开/收起 Telegram 配置">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-cyan-100 to-cyan-200 dark:from-cyan-900/40 dark:to-cyan-800/30 rounded-lg shadow-sm flex-shrink-0 border border-cyan-200/50 dark:border-cyan-700/30 flex items-center justify-center">
                        <i class="ti ti-brand-telegram text-base text-cyan-600 dark:text-cyan-400"></i>
                    </div>
                    <div>
                        <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">Telegram 通知设置</h2>
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置Telegram机器人通知功能</p>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <span class="inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border
                        {% if setting.telegram and setting.telegram.enabled and setting.telegram.token %}
                            text-emerald-600 border-emerald-300 bg-emerald-50 dark:text-emerald-400 dark:border-emerald-700/40 dark:bg-emerald-900/20
                        {% else %}
                            text-slate-500 border-slate-300 bg-slate-50 dark:text-slate-400 dark:border-slate-600/40 dark:bg-slate-800/30
                        {% endif %}">
                        <span class="w-2 h-2 rounded-full
                            {% if setting.telegram and setting.telegram.enabled and setting.telegram.token %}bg-emerald-500{% else %}bg-slate-400{% endif %}"></span>
                        {% if setting.telegram and setting.telegram.enabled and setting.telegram.token %}已启用{% else %}未启用{% endif %}
                    </span>
                    <button onclick="event.stopPropagation();testTelegramNotification()" class="px-3 py-1 text-xs rounded-lg border border-slate-200 dark:border-slate-700 bg-white/70 dark:bg-slate-800/50 hover:bg-white dark:hover:bg-slate-700 transition-colors" title="发送测试消息">测试</button>
                    <div class="flex items-center gap-1 text-xs text-slate-400 dark:text-slate-500">
                        <i class="ti ti-chevron-down text-sm transition-transform duration-200" id="tg-chevron"></i>
                        <span class="hidden sm:inline">展开</span>
                    </div>
                </div>
            </div>

            <!-- 内容（默认折叠） -->
            <div id="tg_body" class="p-4 space-y-4 hidden">
                <!-- 基本设置 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">Bot Token</label>
                        <input type="text"
                               value="{{setting.telegram.token}}"
                               key="telegram.token"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">从BotFather获取的机器人令牌</p>
                    </div>
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">Chat ID 列表 (用,分割)</label>
                        <input type="text"
                               value="{{setting.telegram.chatIds}}"
                               key="telegram.chatIds"
                               isarray=1
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">接收通知的聊天ID，多个用逗号分隔</p>
                    </div>
                </div>

                <!-- 连接方式选择 - iPhone风格 -->
                <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <label class="flex items-center gap-2 text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3">
                        <i class="ti ti-network text-sm text-slate-500"></i>
                        连接方式
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Webhook方式 -->
                        <label class="flex items-start space-x-3 cursor-pointer p-3 bg-white/60 dark:bg-slate-700/30 rounded-lg border border-slate-200/40 dark:border-slate-600/30 hover:bg-white/80 dark:hover:bg-slate-700/50 transition-all duration-200">
                            <input type="radio"
                                   name="telegramMode"
                                   value="webhook"
                                   {%if setting.telegram.webhook%}checked{%endif%}
                                   key="telegram.webhook"
                                   class="mt-1 h-4 w-4 text-purple-500 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 focus:ring-purple-500/50 focus:ring-2">
                            <div>
                                <span class="text-sm font-semibold text-slate-700 dark:text-slate-200 tracking-wide">Webhook方式</span>
                                <p class="text-xs text-slate-600 dark:text-slate-400 mt-1 leading-relaxed">服务器接收推送通知，适合有公网IP的服务器</p>
                            </div>
                        </label>

                        <!-- Polling方式 -->
                        <label class="flex items-start space-x-3 cursor-pointer p-3 bg-white/60 dark:bg-slate-700/30 rounded-lg border border-slate-200/40 dark:border-slate-600/30 hover:bg-white/80 dark:hover:bg-slate-700/50 transition-all duration-200">
                            <input type="radio"
                                   name="telegramMode"
                                   value="polling"
                                   {%if not setting.telegram.webhook%}checked{%endif%}
                                   key="telegram.polling"
                                   class="mt-1 h-4 w-4 text-purple-500 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 focus:ring-purple-500/50 focus:ring-2">
                            <div>
                                <span class="text-sm font-semibold text-slate-700 dark:text-slate-200 tracking-wide">Polling方式</span>
                                <p class="text-xs text-slate-600 dark:text-slate-400 mt-1 leading-relaxed">服务器主动轮询消息，适合无公网IP的环境</p>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- API设置 - iPhone风格 -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">API基础URL (网络问题时修改)</label>
                    <input type="text"
                           value="{%if setting.telegram.baseApiUrl%}{{setting.telegram.baseApiUrl}}{%else%}https://api.telegram.org{%endif%}"
                           key="telegram.baseApiUrl"
                           placeholder="https://api.telegram.org"
                           class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">默认使用官方 API，如果无法访问可以使用反向代理地址，例如: https://botapi.ipxxxx.com</p>
                </div>

                <!-- 启用和测试 - iPhone风格 -->
                <div class="flex items-center justify-between bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <div class="flex items-center space-x-4">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox"
                                   {%if setting.telegram.enabled%}checked{%endif%}
                                   key="telegram.enabled"
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-slate-300 dark:bg-slate-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                        </label>
                        <div>
                            <span class="text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">启用通知</span>
                            <p class="text-xs text-slate-500 dark:text-slate-400 mt-1 leading-relaxed">开启Telegram通知功能</p>
                        </div>
                    </div>
                    <!-- 保留原测试按钮（折叠后同样可用） -->
                    <button onclick="testTelegramNotification()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white rounded-xl shadow-md">测试通知</button>
                </div>



                <!-- 流量阈值与通知等待时间设置 -->
                <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm mb-4">
                    <label class="flex items-center gap-2 text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3">
                        <i class="ti ti-gauge text-sm text-slate-500"></i>
                        流量告警阈值（百分比，逗号分隔）
                    </label>
                    <div class="space-y-3">
                        <input type="text"
                               value="{% if setting.telegram.trafficThresholds %}{{setting.telegram.trafficThresholds.join(',')}}{% else %}80,90,95{% endif %}"
                               key="telegram.trafficThresholds"
                               isarray="true"
                               placeholder="例如：80,90,95"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">全局生效：达到任一阈值时发送一次告警（默认 80%、90%、95%）。</p>
                    </div>
                </div>
                <!-- 通知等待时间设置 -->
                <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <label class="flex items-center gap-2 text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3">
                        <i class="ti ti-clock text-sm text-slate-500"></i>
                        离线通知等待时间
                    </label>
                    <div class="space-y-3">
                        <input type="number"
                               value="{%if setting.telegram.offlineNotificationDelay%}{{setting.telegram.offlineNotificationDelay}}{%else%}30{%endif%}"
                               key="telegram.offlineNotificationDelay"
                               min="5"
                               max="300"
                               step="5"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">服务器离线后延迟多少秒发送通知，避免短暂网络波动导致误报 (5-300秒)</p>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <button onclick="edit()" class="flex-1 sm:flex-none inline-flex items-center justify-center gap-2 px-6 py-3 text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                        <div class="spinner hidden w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <i class="ti ti-check text-sm"></i>
                        <span>保存设置</span>
                    </button>
                    <p class="text-xs text-slate-500 dark:text-slate-400 text-center sm:text-left">
                        设置保存后立即生效，无需重启服务
                    </p>
                </div>
            </div>
        </div>

        <!-- 邮件通知设置卡片（折叠） -->
        <div class="admin-card mt-6">
            <div class="admin-list-header flex items-center justify-between gap-3 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors duration-200 rounded-lg -mx-2 px-2" onclick="toggleCard('email')" title="点击展开/收起邮件配置">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-rose-100 to-rose-200 dark:from-rose-900/40 dark:to-rose-800/30 rounded-lg shadow-sm flex-shrink-0 border border-rose-200/50 dark:border-rose-700/30 flex items-center justify-center">
                        <i class="ti ti-mail text-base text-rose-600 dark:text-rose-400"></i>
                    </div>
                    <div>
                        <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">邮件通知设置</h2>
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置 SMTP 发信与邮件通知</p>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <span id="email_status_label" class="inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border text-slate-500 border-slate-300 bg-slate-50 dark:text-slate-400 dark:border-slate-600/40 dark:bg-slate-800/30">
                        <span id="email_status_dot" class="w-2 h-2 rounded-full bg-slate-400"></span>
                        未启用
                    </span>
                    <button onclick="event.stopPropagation();testEmail()" class="px-3 py-1 text-xs rounded-lg border border-slate-200 dark:border-slate-700 bg-white/70 dark:bg-slate-800/50 hover:bg-white dark:hover:bg-slate-700 transition-colors" title="发送测试邮件">测试</button>
                    <div class="flex items-center gap-1 text-xs text-slate-400 dark:text-slate-500">
                        <i class="ti ti-chevron-down text-sm transition-transform duration-200" id="email-chevron"></i>
                        <span class="hidden sm:inline">展开</span>
                    </div>
                </div>
            </div>

            <div id="email_body" class="p-4 space-y-5 hidden">
                <!-- 发送开关与 SMTP 连接 -->
                <div class="text-xs font-semibold text-slate-600 dark:text-slate-300 tracking-wide">发送开关与 SMTP 连接</div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center gap-3 md:col-span-2">
                        <input type="checkbox" id="email_enabled" class="w-4 h-4">
                        <label for="email_enabled" class="text-sm text-slate-700 dark:text-slate-300">启用邮件通知</label>
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">SMTP 主机</label>
                        <input id="email_host" type="text" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">端口</label>
                        <input id="email_port" type="number" min="1" max="65535" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                    </div>
                    <div class="flex items-center gap-3">
                        <input type="checkbox" id="email_secure" class="w-4 h-4">
                        <label for="email_secure" class="text-sm text-slate-700 dark:text-slate-300">安全连接（SSL/TLS）</label>
                    </div>
                </div>

                <!-- 认证信息 -->
                <div class="text-xs font-semibold text-slate-600 dark:text-slate-300 tracking-wide">认证信息</div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">用户名</label>
                        <input id="email_auth_user" type="text" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">密码（留空保留原值）</label>
                        <input id="email_auth_pass" type="password" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200" placeholder="••••••">
                    </div>
                </div>

                <!-- 发件人信息 -->
                <div class="text-xs font-semibold text-slate-600 dark:text-slate-300 tracking-wide">发件人信息</div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">发件人名称</label>
                        <input id="email_from_name" type="text" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">发件人地址</label>
                        <input id="email_from_address" type="email" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200" placeholder="<EMAIL>">
                    </div>
                </div>

                <!-- 收件人列表 -->
                <div class="text-xs font-semibold text-slate-600 dark:text-slate-300 tracking-wide">收件人列表</div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-3">
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">收件人（逗号分隔）</label>
                        <input id="email_to" type="text" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200" placeholder="<EMAIL>,<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">抄送（逗号分隔）</label>
                        <input id="email_cc" type="text" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">密送（逗号分隔）</label>
                        <input id="email_bcc" type="text" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                    </div>
                </div>



                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">每分钟限速（封）</label>
                        <input id="email_rate" type="number" min="1" max="600" value="60" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                    </div>
                </div>

                <div class="flex flex-wrap items-center gap-3 mt-2">
                    <button onclick="saveEmailConfig()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700 text-white rounded-xl shadow-md">保存设置</button>
                    <button onclick="testEmail()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-white/80 dark:bg-slate-800/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-xl">发送测试邮件</button>
                </div>

                <div class="mt-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300">最近发送日志</h3>
                        <div class="flex items-center gap-2">
                            <input id="email_cleanup_days" type="number" value="30" min="1" class="w-20 px-2 py-1 rounded bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-sm">
                            <button onclick="cleanupEmailLogs()" class="px-3 py-1 text-xs rounded bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700">清理</button>
                        </div>
                    </div>
                    <div class="overflow-x-auto border border-slate-200 dark:border-slate-700 rounded-xl">
                        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                            <thead class="bg-slate-50 dark:bg-slate-800/60">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">时间</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">主题</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">类型</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">状态</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">错误</th>
                                </tr>
                            </thead>
                            <tbody id="email_logs_tbody" class="divide-y divide-slate-200 dark:divide-slate-700 bg-white/60 dark:bg-slate-900/20"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自定义通知任务（按服务器） -->
        <div class="admin-card mt-6">
            <div class="admin-list-header flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/40 dark:to-amber-800/30 rounded-lg shadow-sm flex-shrink-0 border border-amber-200/50 dark:border-amber-700/30 flex items-center justify-center">
                    <i class="ti ti-calendar-stats text-base text-amber-600 dark:text-amber-400"></i>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">自定义通知任务</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">针对任意服务器配置周期、阈值、流量方向与上限</p>
                </div>
            </div>

            <div class="p-4 space-y-4">
                <!-- 新增任务表单 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">服务器</label>
                        <select id="task_sid" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                            {% if servers and servers.length %}
                                {% for s in servers %}
                                    <option value="{{s.sid}}">{{s.name}}</option>
                                {% endfor %}
                            {% else %}
                                <option value="">无服务器</option>
                            {% endif %}
                        </select>
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">周期</label>
                        <select id="task_period" onchange="onTaskPeriodChange()" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                            <option value="monthly" selected>月度</option>
                            <option value="weekly">周度</option>
                            <option value="daily">日度</option>
                        </select>
                    </div>
                    <div id="task_reset_day_wrap">
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">重置日（仅月度）</label>
                        <input id="task_reset_day" type="number" min="1" max="31" placeholder="默认沿用服务器设置" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200" />
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">流量上限（GB，可空）</label>
                        <input id="task_limit_gb" type="number" min="0" step="1" placeholder="留空沿用服务器上限" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200" />
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">阈值（百分比，逗号分隔）</label>
                        <input id="task_thresholds" type="text" placeholder="如：80,90,95（留空用全局或单阈值）" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200" />
                    </div>
                    <div>
                        <label class="block text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">方向</label>
                        <select id="task_direction" class="w-full px-3 py-2 rounded-lg bg-white/80 dark:bg-slate-800/60 border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-200">
                            <option value="both" selected>双向</option>
                            <option value="in">仅入站</option>
                            <option value="out">仅出站</option>
                            <option value="max">单向最大</option>
                        </select>
                    </div>
                    <div class="flex items-center gap-2 mt-6">
                        <input id="task_enabled" type="checkbox" checked class="w-4 h-4 text-purple-500 bg-slate-100 dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded focus:ring-purple-500">
                        <label for="task_enabled" class="text-xs md:text-sm text-slate-700 dark:text-slate-300">启用</label>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <button onclick="createTask()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-amber-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200">
                        <i class="ti ti-plus"></i>
                        新增任务
                    </button>
                    <button onclick="loadTasks()" class="inline-flex items-center gap-2 px-3 py-2 text-xs font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-lg border border-slate-200 dark:border-slate-700">
                        <i class="ti ti-refresh"></i>
                        刷新列表
                    </button>
                </div>

                <!-- 任务列表 -->
                <div class="overflow-x-auto border border-slate-200 dark:border-slate-700 rounded-xl mt-2">
                    <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                        <thead class="bg-slate-50 dark:bg-slate-800/60">
                            <tr>
                                <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">服务器</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">周期</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">方向</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">上限(GB)</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">阈值(%)</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-slate-500">启用</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-slate-500">操作</th>
                            </tr>
                        </thead>
                        <tbody id="tasks_tbody" class="divide-y divide-slate-200 dark:divide-slate-700 bg-white/60 dark:bg-slate-900/20"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 底部脚本 -->
<script>
// 生成深层对象
function gen(obj, keys, value) {
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
            current[keys[i]] = {};
        }
        current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
}

// 编辑设置 - 只操作telegram设置
async function edit() {
    const spinner = document.querySelector('.spinner');
    
    try {
        if (spinner) spinner.classList.remove('hidden');
        
        var setting = {};
        for(var x of document.querySelectorAll("[key]")){
            var val = x.value;
            if(x.type=="checkbox")val=x.checked;
            if(x.getAttribute("isarray"))val=val.split(",");
            gen(setting,x.getAttribute("key").split('.'),val);
        }

        const response = await fetch('/admin/notification/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(setting)
        });

        if (response.ok) {
            notice('设置保存成功', 'success');
            updateTelegramStatus(); // 更新状态徽标
            if (spinner) spinner.classList.add('hidden');
        } else {
            const data = await response.json();
            notice(data.msg || '保存设置失败', 'error');
            if (spinner) spinner.classList.add('hidden');
        }
    } catch (error) {
        console.error('保存设置失败:', error);
        notice('保存设置失败', 'error');
        if (spinner) spinner.classList.add('hidden');
    }
}

// 测试Telegram通知
async function testTelegramNotification() {
    try {
        // 复用edit函数的设置收集逻辑
        var setting = {};
        for(var x of document.querySelectorAll("[key]")){
            var val = x.value;
            if(x.type=="checkbox")val=x.checked;
            if(x.getAttribute("isarray"))val=val.split(",");
            gen(setting,x.getAttribute("key").split('.'),val);
        }

        const response = await fetch('/admin/notification/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({telegram: setting.telegram})
        });

        // 检查HTTP状态
        if (response.ok) {
            notice('测试通知已发送', 'success');
        } else {
            const data = await response.json();
            notice(data.msg || '发送测试通知失败', 'error');
        }
    } catch (error) {
        console.error('发送测试通知失败:', error);
        notice('发送测试通知失败', 'error');
    }
}

// ===== 自定义任务前端逻辑 =====
function onTaskPeriodChange() {
    const period = document.getElementById('task_period').value;
    const wrap = document.getElementById('task_reset_day_wrap');
    if (period === 'monthly') wrap.classList.remove('hidden');
    else wrap.classList.add('hidden');
}

function parseThresholds(str) {
    if (!str) return [];
    return Array.from(new Set(str.split(',').map(s => parseInt(s.trim(), 10)).filter(v => !isNaN(v) && v > 0 && v < 100))).sort((a,b)=>a-b);
}

async function createTask() {
    try {
        const sid = document.getElementById('task_sid').value;
        if (!sid) return notice('请选择服务器', 'error');
        const period = document.getElementById('task_period').value;
        const reset_day = document.getElementById('task_reset_day').value ? parseInt(document.getElementById('task_reset_day').value, 10) : null;
        const dir = document.getElementById('task_direction').value;
        const enabled = document.getElementById('task_enabled').checked;
        const limitGB = parseFloat(document.getElementById('task_limit_gb').value);
        const traffic_limit = isNaN(limitGB) ? null : Math.floor(limitGB * 1024 * 1024 * 1024);
        const thresholds = parseThresholds(document.getElementById('task_thresholds').value);

        const res = await fetch('/admin/notification/tasks', {
            method: 'POST', headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sid, period, reset_day, direction: dir, enabled, traffic_limit, thresholds })
        });
        const data = await res.json();
        if (data.code === 1) {
            notice('创建任务成功', 'success');
            await loadTasks();
        } else {
            notice(data.msg || '创建任务失败', 'error');
        }
    } catch (e) {
        console.error(e);
        notice('创建任务失败', 'error');
    }
}

function formatGB(val) {
    if (!val || val <= 0) return '-';
    return (val / 1024 / 1024 / 1024).toFixed(0);
}

const SERVER_LIST = JSON.parse(document.getElementById('servers-json')?.textContent || '[]');
const SERVER_MAP = (Array.isArray(SERVER_LIST) ? SERVER_LIST : [])
    .reduce((m, s) => { if (s && s.sid != null) m[s.sid] = s.name || String(s.sid); return m; }, {});
function serverNameBySid(sid) {
    return SERVER_MAP[sid] || sid;
}

function mapPeriodLabel(p) {
    switch ((p || 'monthly')) {
        case 'daily': return '日度';
        case 'weekly': return '周度';
        case 'monthly':
        default: return '月度';
    }
}
function mapDirectionLabel(d) {
    switch ((d || 'both')) {
        case 'in': return '仅入站';
        case 'out': return '仅出站';
        case 'max': return '单向最大';
        case 'both':
        default: return '双向';
    }
}

function renderTasks(tasks) {
    const tbody = document.getElementById('tasks_tbody');
    tbody.innerHTML = '';
    (tasks || []).forEach(t => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td class="px-3 py-2 text-sm text-slate-700 dark:text-slate-300">${serverNameBySid(t.sid)}</td>
            <td class="px-3 py-2 text-sm">${mapPeriodLabel(t.period)}</td>
            <td class="px-3 py-2 text-sm">${mapDirectionLabel(t.direction)}</td>
            <td class="px-3 py-2 text-sm">${formatGB(t.traffic_limit)}</td>
            <td class="px-3 py-2 text-sm">${(t.thresholds || []).join(',') || '-'}</td>
            <td class="px-3 py-2 text-sm">${t.enabled ? '是' : '否'}</td>
            <td class="px-3 py-2 text-sm text-right">
                <button class="px-2 py-1 text-xs rounded bg-slate-100 dark:bg-slate-800" onclick="toggleTask('${t.id}', ${t.enabled ? 'false' : 'true'})">${t.enabled ? '禁用' : '启用'}</button>
                <button class="ml-2 px-2 py-1 text-xs rounded bg-red-500 text-white" onclick="deleteTask('${t.id}')">删除</button>
            </td>`;
        tbody.appendChild(tr);
    });
}

async function loadTasks() {
    try {
        const res = await fetch('/admin/notification/tasks');
        const data = await res.json();
        if (data.code === 1) renderTasks(data.data || []);
    } catch (e) { console.error(e); }
}

async function deleteTask(id) {
    if (!confirm('确定删除该任务？')) return;
    const res = await fetch(`/admin/notification/tasks/${id}/delete`, { method: 'POST' });
    const data = await res.json();
    if (data.code === 1) { notice('删除成功', 'success'); loadTasks(); }
    else notice(data.msg || '删除失败', 'error');
}

async function toggleTask(id, enabled) {
    const res = await fetch(`/admin/notification/tasks/${id}/toggle`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ enabled }) });
    const data = await res.json();
    if (data.code === 1) { notice('更新成功', 'success'); loadTasks(); }
    else notice(data.msg || '更新失败', 'error');
}

// 更新Telegram卡片状态徽标
async function updateTelegramStatus() {
    try {
        const response = await fetch('/admin/notification/telegram/config');
        const result = await response.json();
        const tg = result.code === 1 ? result.data || {} : {};
        const isEnabled = !!(tg.enabled && tg.token);

        // 更新状态徽标
        const statusBadge = document.querySelector('.admin-list-header span');
        const statusDot = statusBadge?.querySelector('span');

        if (statusBadge && statusDot) {
            // 更新徽标样式
            statusBadge.className = `inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border ${
                isEnabled
                    ? 'text-emerald-600 border-emerald-300 bg-emerald-50 dark:text-emerald-400 dark:border-emerald-700/40 dark:bg-emerald-900/20'
                    : 'text-slate-500 border-slate-300 bg-slate-50 dark:text-slate-400 dark:border-slate-600/40 dark:bg-slate-800/30'
            }`;

            // 更新状态点
            statusDot.className = `w-2 h-2 rounded-full ${isEnabled ? 'bg-emerald-500' : 'bg-slate-400'}`;

            // 更新状态文本
            statusBadge.lastChild.textContent = isEnabled ? '已启用' : '未启用';
        }
    } catch (error) {
        console.error('更新Telegram状态失败:', error);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    onTaskPeriodChange();
    loadTasks();
    loadEmailConfig();
    loadEmailLogs();
    loadUnifiedNotificationTypes();
    updateTelegramStatus(); // 更新Telegram状态徽标

    // 默认收起通道卡片，并确保UI状态正确
    try{
        Q('tg_body').classList.add('hidden');
        Q('email_body').classList.add('hidden');

        // 确保箭头图标和文本处于正确的初始状态
        const tgChevron = Q('tg-chevron');
        const emailChevron = Q('email-chevron');

        if (tgChevron) {
            tgChevron.classList.add('ti-chevron-down');
            tgChevron.style.transform = 'rotate(0deg)';
        }
        if (emailChevron) {
            emailChevron.classList.add('ti-chevron-down');
            emailChevron.style.transform = 'rotate(0deg)';
        }

        // 确保展开文本正确
        const expandTexts = document.querySelectorAll('.admin-list-header span:contains("展开")');
        expandTexts.forEach(span => {
            if (span.textContent.includes('收起')) {
                span.textContent = '展开';
            }
        });
    }catch(e){}
});

// ===== 邮件配置逻辑 =====
function splitCSV(str){ if(!str) return []; return str.split(',').map(s=>s.trim()).filter(Boolean); }
function joinCSV(arr){ return (arr||[]).join(','); }
function isValidEmail(e){ return /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/.test(e); }
function validateEmailList(listStr){ const arr=splitCSV(listStr); for(const e of arr){ if(!isValidEmail(e)) return { ok:false, bad:e }; } return { ok:true, bad:null } }
function setFieldError(id, msg){ const el=Q(id); if(!el) return; el.classList.add('border-red-500','focus:border-red-500','focus:ring-red-500/50'); let hint=Q(id+'_error'); if(!hint){ hint=document.createElement('div'); hint.id=id+'_error'; hint.className='mt-1 text-xs text-red-500'; el.parentElement.appendChild(hint); } hint.textContent=msg||''; }
function clearFieldError(id){ const el=Q(id); if(!el) return; el.classList.remove('border-red-500','focus:border-red-500','focus:ring-red-500/50'); const hint=Q(id+'_error'); if(hint) hint.remove(); }
async function loadEmailConfig(){
    try{
        const respConfig=await fetch('/admin/notification/email/config');
        const j=await respConfig.json();
        if(j.code!==1) return;
        const c=j.data||{};

        // 保存到全局变量供其他函数使用
        window.currentEmailConfig = c;

        Q('email_enabled').checked=!!c.enabled;
        Q('email_host').value=c.host||'';
        Q('email_port').value=c.port||'';
        Q('email_secure').checked=!!c.secure;
        Q('email_auth_user').value=c.auth_user||'';
        Q('email_from_name').value=c.from_name||'';
        Q('email_from_address').value=c.from_address||'';
        Q('email_to').value=joinCSV(c.default_to||[]);
        Q('email_cc').value=joinCSV(c.default_cc||[]);
        Q('email_bcc').value=joinCSV(c.default_bcc||[]);
        Q('email_rate').value=c.rate_limit_per_min||60;
        // 更新头部状态徽标
        const ok = !!(c && c.enabled && c.from_address && (c.default_to||[]).length>0);
        const lab=Q('email_status_label'), dot=Q('email_status_dot');
        if (lab && dot){
            if (ok){
                lab.className='inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border text-emerald-600 border-emerald-300 bg-emerald-50 dark:text-emerald-400 dark:border-emerald-700/40 dark:bg-emerald-900/20';
                dot.className='w-2 h-2 rounded-full bg-emerald-500';
                lab.innerHTML = '<span id="email_status_dot" class="w-2 h-2 rounded-full bg-emerald-500"></span>已启用';
            }else{
                lab.className='inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border text-slate-500 border-slate-300 bg-slate-50 dark:text-slate-400 dark:border-slate-600/40 dark:bg-slate-800/30';
                dot.className='w-2 h-2 rounded-full bg-slate-400';
                lab.innerHTML = '<span id="email_status_dot" class="w-2 h-2 rounded-full bg-slate-400"></span>未启用';
            }
        }
        // 通知类型现在由统一管理区域处理，无需在此设置
    }catch(e){console.error(e);}
}
function collectEmailConfig(){
    return {
        enabled: Q('email_enabled').checked,
        host: Q('email_host').value.trim(),
        port: parseInt(Q('email_port').value||'0',10)||null,
        secure: Q('email_secure').checked,
        auth_user: Q('email_auth_user').value.trim(),
        auth_pass: Q('email_auth_pass').value.trim(),
        from_name: Q('email_from_name').value.trim(),
        from_address: Q('email_from_address').value.trim(),
        default_to: splitCSV(Q('email_to').value),
        default_cc: splitCSV(Q('email_cc').value),
        default_bcc: splitCSV(Q('email_bcc').value),
        // 通知类型现在由统一管理区域处理，从全局变量获取当前配置
        notification_types: window.currentEmailConfig?.notification_types || {},
        rate_limit_per_min: parseInt(Q('email_rate').value||'60',10)
    };
}
async function saveEmailConfig(){
    try{
        const cfg=collectEmailConfig();
        // 前端校验：发件人与收件人邮箱
        let hasErr=false;
        clearFieldError('email_from_address');
        clearFieldError('email_to');
        clearFieldError('email_cc');
        clearFieldError('email_bcc');
        if(!isValidEmail(cfg.from_address)) { setFieldError('email_from_address','请输入有效的发件人邮箱'); hasErr=true; }
        let check=validateEmailList(Q('email_to').value); if(!check.ok){ setFieldError('email_to',`无效收件人：${check.bad}`); hasErr=true; }
        check=validateEmailList(Q('email_cc').value); if(!check.ok){ setFieldError('email_cc',`无效抄送：${check.bad}`); hasErr=true; }
        check=validateEmailList(Q('email_bcc').value); if(!check.ok){ setFieldError('email_bcc',`无效密送：${check.bad}`); hasErr=true; }
        if(hasErr){ notice('请修正邮箱格式后再保存','error'); return; }
        const respSave=await fetch('/admin/notification/email/save',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(cfg)});
        const j=await respSave.json();
        if(j.code===1){ notice('保存成功','success'); loadEmailConfig(); }
        else notice(j.msg||'保存失败','error');
    }catch(e){ console.error(e); notice('保存失败','error'); }
}
async function testEmail(){
    try{
        const resp=await fetch('/admin/notification/test-email',{method:'POST'});
        const j=await resp.json();
        if(j.code===1) notice('测试邮件已发送','success'); else notice(j.msg||'发送失败','error');
    }catch(e){ console.error(e); notice('发送失败','error'); }
}
async function loadEmailLogs(){
    try{
        const respLogs=await fetch('/admin/notification/email/logs');
        const j=await respLogs.json();
        if(j.code!==1) return;
        const tbody=Q('email_logs_tbody');
        tbody.innerHTML='';
        for(const row of (j.data.rows||[])){
            const tr=document.createElement('tr');
            const dt=(ts=>{try{if(!ts)return '';const d=new Date(ts*1000);return d.toLocaleString();}catch{return ''}})(row.sent_at||row.created_at);
            tr.innerHTML=`<td class="px-3 py-2 text-sm">${dt}</td>
                <td class="px-3 py-2 text-sm">${row.subject||''}</td>
                <td class="px-3 py-2 text-sm">${row.type||''}</td>
                <td class="px-3 py-2 text-sm">${row.status||''}</td>
                <td class="px-3 py-2 text-sm">${row.error||''}</td>`;
            tbody.appendChild(tr);
        }
    }catch(e){ console.error(e); }
}
async function cleanupEmailLogs(){
    try{
        const days=parseInt(Q('email_cleanup_days').value||'30',10);
        const respCleanup=await fetch('/admin/notification/email/logs/cleanup',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({days})});
        const j=await respCleanup.json();
        if(j.code===1){ notice('清理完成','success'); loadEmailLogs(); } else notice(j.msg||'清理失败','error');
    }catch(e){ console.error(e); notice('清理失败','error'); }
}

// 辅助函数 - DOM元素获取
function Q(id) {
    return document.getElementById(id);
}

// 折叠控制 - 确保全局可用，增强UI反馈
window.toggleCard = function(which){
    const id = which==='tg'?'tg_body':(which==='email'?'email_body':null);
    if(!id) return;

    const el = Q(id);
    if(!el) return;

    const isHidden = el.classList.contains('hidden');
    el.classList.toggle('hidden');

    // 更新箭头图标和文本
    const chevronId = which === 'tg' ? 'tg-chevron' : 'email-chevron';
    const chevron = Q(chevronId);
    if (chevron) {
        if (isHidden) {
            // 展开状态：箭头向上
            chevron.classList.remove('ti-chevron-down');
            chevron.classList.add('ti-chevron-up');
            chevron.style.transform = 'rotate(180deg)';
        } else {
            // 收起状态：箭头向下
            chevron.classList.remove('ti-chevron-up');
            chevron.classList.add('ti-chevron-down');
            chevron.style.transform = 'rotate(0deg)';
        }
    }

    // 更新展开/收起文本
    const textSpan = chevron?.parentElement?.querySelector('span');
    if (textSpan) {
        textSpan.textContent = isHidden ? '收起' : '展开';
    }
};

// ===== 统一通知类型矩阵 =====
const NOTIFICATION_ITEMS = [
    { key:'serverOnline', label:'服务器上线', icon:'ti-circle-check', desc:'服务器恢复在线时发送通知' },
    { key:'serverOffline', label:'服务器下线', icon:'ti-circle-x', desc:'服务器离线时发送通知' },
    { key:'trafficLimit', label:'流量超限', icon:'ti-gauge', desc:'服务器流量达到阈值时发送通知' },
    { key:'testNotification', label:'测试通知', icon:'ti-bell', desc:'用于测试通知功能是否正常' },
    { key:'statusSummary', label:'状态汇总', icon:'ti-report', desc:'定期发送服务器状态汇总报告' }
];
async function loadUnifiedNotificationTypes(){
    try{
        const [tgRes, emRes] = await Promise.all([
            fetch('/admin/notification/telegram/config'),
            fetch('/admin/notification/email/config')
        ]);
        const tgJson = await tgRes.json();
        const emJson = await emRes.json();
        const tg = tgJson.code===1 ? tgJson.data||{} : {};
        const em = emJson.code===1 ? emJson.data||{} : {};
        const tgAvailable = !!(tg && tg.enabled && tg.token);
        const emAvailable = !!(em && em.enabled && em.from_address && (em.default_to||[]).length>0);

        // 更新通道计数
        const activeChannels = (tgAvailable ? 1 : 0) + (emAvailable ? 1 : 0);
        const channelCountElement = Q('active-channels-count');
        if (channelCountElement) {
            channelCountElement.textContent = activeChannels;
        }

        // 渲染统一通知类型矩阵
        renderUnifiedNotificationMatrix(tg, em, tgAvailable, emAvailable);

    }catch(e){ console.error(e); }
}

// 渲染统一通知类型矩阵表格
function renderUnifiedNotificationMatrix(tg, em, tgAvailable, emAvailable) {
    const matrixContainer = Q('unified-notification-matrix');
    if (!matrixContainer) return;

    matrixContainer.innerHTML = '';

    NOTIFICATION_ITEMS.forEach(item => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors';

        // 通知类型名称列
        const nameCell = document.createElement('td');
        nameCell.className = 'py-4 px-4';
        nameCell.innerHTML = `
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="ti ${item.icon} text-sm text-slate-600 dark:text-slate-400"></i>
                </div>
                <div>
                    <div class="text-sm font-medium text-slate-800 dark:text-slate-200">${item.label}</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400 mt-0.5 hidden sm:block">${item.desc}</div>
                </div>
            </div>
        `;

        // Telegram列
        const tgCell = document.createElement('td');
        tgCell.className = 'py-4 px-4 text-center';
        if (tgAvailable) {
            const checked = !!(tg.notificationTypes ? tg.notificationTypes[item.key]!==false : true);
            tgCell.innerHTML = `
                <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="nt_tg_${item.key}" ${checked?'checked':''}
                           class="w-4 h-4 text-cyan-500 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 rounded focus:ring-cyan-500 focus:ring-2"
                           onchange="toggleNotificationType('tg', '${item.key}', this.checked)">
                    <span class="sr-only">Telegram ${item.label}</span>
                </label>
            `;
        } else {
            tgCell.innerHTML = `
                <div class="flex items-center justify-center">
                    <span class="text-xs text-slate-400 dark:text-slate-500 bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">未配置</span>
                </div>
            `;
        }

        // 邮件列
        const emCell = document.createElement('td');
        emCell.className = 'py-4 px-4 text-center';
        if (emAvailable) {
            const checked = !!(em.notification_types ? em.notification_types[item.key]!==false : true);
            emCell.innerHTML = `
                <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="nt_em_${item.key}" ${checked?'checked':''}
                           class="w-4 h-4 text-rose-500 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 rounded focus:ring-rose-500 focus:ring-2"
                           onchange="toggleNotificationType('em', '${item.key}', this.checked)">
                    <span class="sr-only">邮件 ${item.label}</span>
                </label>
            `;
        } else {
            emCell.innerHTML = `
                <div class="flex items-center justify-center">
                    <span class="text-xs text-slate-400 dark:text-slate-500 bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">未配置</span>
                </div>
            `;
        }

        // 全选列
        const allCell = document.createElement('td');
        allCell.className = 'py-4 px-4 text-center';
        const hasAnyChannel = tgAvailable || emAvailable;
        if (hasAnyChannel) {
            allCell.innerHTML = `
                <button onclick="toggleAllChannelsForType('${item.key}')"
                        class="inline-flex items-center justify-center w-8 h-8 rounded-lg border border-slate-300 dark:border-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
                        title="切换所有通道的${item.label}">
                    <i class="ti ti-toggle-left text-sm text-slate-600 dark:text-slate-400"></i>
                </button>
            `;
        } else {
            allCell.innerHTML = `
                <div class="flex items-center justify-center">
                    <span class="text-xs text-slate-400">-</span>
                </div>
            `;
        }

        row.appendChild(nameCell);
        row.appendChild(tgCell);
        row.appendChild(emCell);
        row.appendChild(allCell);
        matrixContainer.appendChild(row);
    });
}
async function saveUnifiedNotificationTypes(){
    try{
        // 读取当前配置
        const tgRes = await fetch('/admin/notification/telegram/config');
        const tgJson = await tgRes.json();
        const tele = tgJson.code===1 ? (tgJson.data||{}) : {};
        const emRes = await fetch('/admin/notification/email/config');
        const emJson = await emRes.json();
        const email = emJson.code===1 ? (emJson.data||{}) : {};

        // 构建新的 types
        const tgAvailable = !!(tele && tele.enabled && tele.token);
        const emAvailable = !!(email && email.enabled && email.from_address && (email.default_to||[]).length>0);
        if (tgAvailable){
            tele.notificationTypes = tele.notificationTypes || {};
            for(const item of NOTIFICATION_ITEMS){
                const el = Q('nt_tg_'+item.key);
                if (el) tele.notificationTypes[item.key] = el.checked;
            }
            // 保存 Telegram（复用保存接口）
            await fetch('/admin/notification/save',{ method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ telegram: tele }) });
        }
        if (emAvailable){
            email.notification_types = email.notification_types || {};
            for(const item of NOTIFICATION_ITEMS){
                const el = Q('nt_em_'+item.key);
                if (el) email.notification_types[item.key] = el.checked;
            }
            await fetch('/admin/notification/email/save',{ method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(email) });
        }
        notice('类型开关已保存','success');
        loadUnifiedNotificationTypes(); // 重新加载通知类型状态
    }catch(e){ console.error(e); notice('保存失败','error'); }
}

// 新增：单个通知类型切换
function toggleNotificationType(channel, type, enabled) {
    console.log(`切换通知类型: ${channel}.${type} = ${enabled}`);
    // 实时反馈，无需等待保存
}

// 新增：切换某个通知类型的所有通道
function toggleAllChannelsForType(type) {
    const tgCheckbox = Q(`nt_tg_${type}`);
    const emCheckbox = Q(`nt_em_${type}`);

    // 检查当前状态
    const tgChecked = tgCheckbox ? tgCheckbox.checked : false;
    const emChecked = emCheckbox ? emCheckbox.checked : false;
    const allChecked = tgChecked && emChecked;

    // 切换状态：如果全部选中则全部取消，否则全部选中
    const newState = !allChecked;

    if (tgCheckbox) {
        tgCheckbox.checked = newState;
        toggleNotificationType('tg', type, newState);
    }
    if (emCheckbox) {
        emCheckbox.checked = newState;
        toggleNotificationType('em', type, newState);
    }

    notice(`已${newState ? '启用' : '禁用'}所有通道的${NOTIFICATION_ITEMS.find(item => item.key === type)?.label}`, 'info');
}

// 新增：恢复默认通知类型设置
async function resetNotificationTypes() {
    if (!confirm('确定要恢复所有通知类型的默认设置吗？这将启用所有通知类型。')) {
        return;
    }

    try {
        // 获取当前配置
        const [tgRes, emRes] = await Promise.all([
            fetch('/admin/notification/telegram/config'),
            fetch('/admin/notification/email/config')
        ]);
        const tgJson = await tgRes.json();
        const emJson = await emRes.json();
        const tele = tgJson.code===1 ? (tgJson.data||{}) : {};
        const email = emJson.code===1 ? (emJson.data||{}) : {};

        const tgAvailable = !!(tele && tele.enabled && tele.token);
        const emAvailable = !!(email && email.enabled && email.from_address && (email.default_to||[]).length>0);

        // 设置默认值（全部启用）
        if (tgAvailable) {
            tele.notificationTypes = {};
            NOTIFICATION_ITEMS.forEach(item => {
                tele.notificationTypes[item.key] = true;
            });
            await fetch('/admin/notification/save',{ method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ telegram: tele }) });
        }

        if (emAvailable) {
            email.notification_types = {};
            NOTIFICATION_ITEMS.forEach(item => {
                email.notification_types[item.key] = true;
            });
            await fetch('/admin/notification/email/save',{ method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(email) });
        }

        notice('已恢复默认设置','success');
        loadUnifiedNotificationTypes(); // 重新加载
    } catch(e) {
        console.error(e);
        notice('恢复默认设置失败','error');
    }
}
</script>

    </div>
</div>
{%endblock%}
