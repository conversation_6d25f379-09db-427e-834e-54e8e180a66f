{"name": "<PERSON><PERSON><PERSON>", "version": "***********.beta", "description": "A simple server status monitor", "main": "dstatus.js", "bin": "dstatus.js", "scripts": {"start": "node dstatus.js", "dev:raw": "concurrently \"npm run watch:css\" \"nodemon dstatus.js\"", "dev": "node scripts/dev-start.js", "restart": "npm run kill:services && npm run dev", "kill:services": "node scripts/kill-services.js", "build:css": "node build-css.js", "watch:css": "tailwindcss -i ./static/css/tailwind.css -o ./static/css/style.min.css --watch --verbose", "lint:css": "stylelint 'static/css/**/*.css'", "lint:css:fix": "stylelint 'static/css/**/*.css' --fix", "test": "mocha tests/**/*.test.js --recursive", "test:watch": "mocha tests/**/*.test.js --recursive --watch", "test:coverage": "nyc mocha tests/**/*.test.js --recursive", "test:migration": "node tests/migration_test.js", "test:compatibility": "node tests/integration/database-compatibility.test.js", "test:performance": "node tests/performance/benchmark/database-operations.js", "test:stress": "node tests/performance/stress/concurrent-access.js", "test:legacy": "node tests/performance/benchmark/database-operations.js && node tests/performance/stress/concurrent-access.js", "test:server": "NODE_ENV=test PORT=5556 DB_PATH=data/test.db node dstatus.js", "migrate": "node scripts/migrate.js", "migrate:test": "node tests/migration_test.js", "generate-docs": "node scripts/generate-api-docs.js", "docs": "npm run generate-docs", "cleanup": "node scripts/cleanup-database-enhanced.js", "cleanup:dry-run": "node scripts/cleanup-database-enhanced.js --dry-run", "clean": "rm -rf node_modules package-lock.json", "build": "scripts/build-local.sh", "build:local": "scripts/build-local.sh", "build:linux": "@yao-pkg/pkg . -t node20-linux-x64 -o dist/dstatus-linux", "build:linux-arm64": "@yao-pkg/pkg . -t node20-linux-arm64 -o dist/dstatus-linux-arm64", "build:macos": "@yao-pkg/pkg . -t node20-macos-arm64 -o dist/dstatus-macos", "build:all": "@yao-pkg/pkg . -t node20-linux-x64,node20-linux-arm64,node20-macos-arm64 -o dist/dstatus", "build:binary": "@yao-pkg/pkg . -t node20-macos-arm64 -o dist/dstatus", "build:binary:linux": "@yao-pkg/pkg . -t node20-linux-x64 -o dist/dstatus-linux", "verify:beta": "./scripts/verify-beta-deployment.sh", "verify:beta:local": "./scripts/verify-beta-deployment.sh ./dstatus-linux-x64", "localize:cdn": "node scripts/localize-cdn.js"}, "keywords": [], "engines": {"node": "^20.17.0 || >=22.9.0"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "@hyperbrowser/sdk": "^0.46.0", "axios": "^1.9.0", "better-sqlite3": "^11.9.1", "body-parser": "^1.19.0", "compression": "^1.8.1", "cookie-parser": "^1.4.5", "express": "^4.18.2", "express-fileupload": "^1.5.1", "express-session": "^1.17.3", "express-ws": "^5.0.2", "js-yaml": "^4.1.0", "md5": "^2.3.0", "node-fetch": "^2.7.0", "node-schedule": "^2.0.0", "node-ssh": "^13.1.0", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^6.10.1", "nunjucks": "^3.2.3", "pg": "^8.12.0", "pg-connection-string": "^2.6.2", "uuid": "^8.3.2"}, "devDependencies": {"@tabler/icons-webfont": "^3.34.1", "@yao-pkg/pkg": "^6.5.1", "autoprefixer": "^10.4.17", "chai": "^5.2.1", "chokidar": "^3.6.0", "colors": "^1.4.0", "concurrently": "^9.1.2", "cssnano": "^6.0.3", "mocha": "^11.7.1", "nodemon": "^3.1.9", "nyc": "^17.1.0", "postcss": "^8.4.33", "postcss-import": "^16.1.0", "simple-icons": "^15.10.0", "sinon": "^21.0.0", "sqlite3": "^5.1.7", "stylelint": "^16.23.1", "stylelint-config-standard": "^39.0.0", "swagger-autogen": "^2.23.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwindcss": "^3.4.1"}, "nodemonConfig": {"watch": ["*.js", "modules/", "views/", "database/"], "ext": "js,json,html", "ignore": ["node_modules/", "data/**/*", "*.db", "*.db-wal", "*.db-shm", "logs/**/*", "backup/**/*", "dist/**/*", ".vscode/", ".idea/", "*.swp", "*.swo", "*~", ".DS_Store", "*.tmp", "*.temp"], "delay": 1000, "legacyWatch": false}, "pkg": {"scripts": ["modules/**/*.js", "database/**/*.js"], "assets": ["views/**/*", "static/**/*", "node_modules/better-sqlite3/build/Release/better_sqlite3.node", "node_modules/better-sqlite3/build/Release/*.node", "native-modules/**/*.node"], "ignore": ["config/**/*", "data/**/*", "logs/**/*", "*.log", "scripts/build-*.sh", "scripts/deploy-*.sh", "scripts/verify-*.sh", "scripts/inject-version.js", "scripts/debug-*.js", "scripts/performance-*.js", "scripts/monitor-*.js", "scripts/analyze-*.js", "scripts/diagnose-*.js", "scripts/generate-*.js", "scripts/generate-*.py", "scripts/wal-*.js", "scripts/*-emergency-*.sh", "scripts/puppeteer-*.js", "archive/**/*", "arm-beta/**/*", "tdd-workspace/**/*"], "targets": ["node20-macos-arm64", "node20-linux-x64"], "outputPath": "dist"}}